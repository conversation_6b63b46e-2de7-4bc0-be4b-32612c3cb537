* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

body {
  background: #f4f6f9;
  color: #333;
}

.container {
  max-width: 900px;
  margin: 20px auto;
  background: #fff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

header {
  text-align: center;
  margin-bottom: 20px;
}

header h1 {
  color: #0d6efd;
  font-size: 2.2rem;
  margin-bottom: 5px;
}

.contact {
  font-size: 0.9rem;
  color: #666;
}

.contact a {
  color: #0d6efd;
  text-decoration: none;
}

h2 {
  color: #0d6efd;
  margin: 20px 0 10px;
  font-size: 1.5rem;
  border-bottom: 2px solid #0d6efd;
  display: inline-block;
}

.projects .project {
  background: #f9fbfd;
  padding: 10px;
  margin: 10px 0;
  border-left: 4px solid #0d6efd;
  transition: transform 0.3s ease-in-out;
}

.projects .project:hover {
  transform: translateX(5px);
  background: #eef4ff;
}

.achievements ul, .skills ul {
  list-style: square inside;
  padding-left: 10px;
}

footer {
  text-align: center;
  margin-top: 20px;
  font-size: 0.9rem;
  color: #555;
}
