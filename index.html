<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON> - Resume</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div class="container">
    <header>
      <h1><PERSON><PERSON><PERSON></h1>
      <p class="contact">
        📍 Kolkata, India | ✉️ <EMAIL> | ☎️ +91-XXXXXXXXXX <br>
        🌐 <a href="#">GitHub</a> | <a href="#">LinkedIn</a> | <a href="#">Portfolio</a>
      </p>
    </header>

    <section class="summary">
      <h2>Profile Summary</h2>
      <p>
        Result-driven <b>Web Developer</b> and <b>Problem Solver</b> with expertise in 
        <b>frontend & full-stack development</b>, strong knowledge of DSA 
        (300+ problems solved), and proven experience in hackathons & API integrations.
      </p>
    </section>

    <section class="skills">
      <h2>Technical Skills</h2>
      <ul>
        <li><b>Languages:</b> C, C++, Java, JavaScript, Python</li>
        <li><b>Web Development:</b> HTML5, CSS3, Bootstrap, React.js, Node.js, Flask</li>
        <li><b>Database:</b> MongoDB, Firebase Firestore</li>
        <li><b>Tools:</b> Git, Postman, VS Code, Vivado, Selenium</li>
      </ul>
    </section>

    <section class="projects">
      <h2>Projects</h2>
      <div class="project">
        <h3>Sahyog – Advanced Medical Appointment System</h3>
        <p>
          Built with <b>HTML, CSS, JS, Firebase Firestore & Google Calendar API</b>.
          Achieved <b>3rd Position at Postman API Hackathon (BITS Pilani)</b>.
        </p>
      </div>
      <div class="project">
        <h3>Stay Guide – PG & Room Finder Platform</h3>
        <p>Multi-page platform with filters for budget & distance. Built in <b>HTML, CSS, JS</b>, later upgrading to React.</p>
      </div>
      <div class="project">
        <h3>Trip Zennie – All-in-One Travel Planner</h3>
        <p>Automated trip planning using <b>Flask, Selenium, Gemini LLM</b>.</p>
      </div>
      <div class="project">
        <h3>Prayas – AYUSH Plants Platform</h3>
        <p>Recognized plants with filters & locations. <b>Cleared 2 stages of Hack Heritage Hackathon</b>.</p>
      </div>
    </section>

    <section class="achievements">
      <h2>Achievements</h2>
      <ul>
        <li>3rd Position – Postman API Hackathon (BITS Pilani)</li>
        <li>Stage 2 Cleared – Hack Heritage Hackathon (AYUSH Project)</li>
        <li>Winner – Code for Bharat Hackathon</li>
        <li>300+ DSA problems solved</li>
        <li>NDA SSB Conferences – Allahabad (33) & Bhopal (21)</li>
      </ul>
    </section>

    <section class="education">
      <h2>Education</h2>
      <p><b>Heritage Institute of Technology, Kolkata</b><br>
      B.Tech, Computer Science (2nd Year) – Expected Graduation: 2027</p>
    </section>

    <footer>
      <p>📧 <EMAIL> | 🌐 Portfolio | 🔗 GitHub | 🔗 LinkedIn</p>
    </footer>
  </div>
  <script src="script.js"></script>
</body>
</html>
