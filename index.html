<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON> - Full Stack Developer</title>
  <link rel="stylesheet" href="style.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body>
  <!-- Animated Background -->
  <div class="animated-bg">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
  </div>

  <!-- Navigation -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-logo">
        <span class="logo-text">KRP</span>
      </div>
      <ul class="nav-menu">
        <li><a href="#home" class="nav-link">Home</a></li>
        <li><a href="#about" class="nav-link">About</a></li>
        <li><a href="#skills" class="nav-link">Skills</a></li>
        <li><a href="#projects" class="nav-link">Projects</a></li>
        <li><a href="#achievements" class="nav-link">Achievements</a></li>
        <li><a href="#contact" class="nav-link">Contact</a></li>
      </ul>
      <div class="hamburger">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="hero">
    <div class="hero-container">
      <div class="hero-content" data-aos="fade-up">
        <div class="hero-image">
          <div class="image-container">
            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face" alt="Kaushal Raj Pandey" class="profile-img">
            <div class="image-overlay"></div>
          </div>
        </div>
        <h1 class="hero-title">
          <span class="greeting">Hello, I'm</span>
          <span class="name">Kaushal Raj Pandey</span>
          <span class="role" id="typewriter"></span>
        </h1>
        <p class="hero-description">
          Passionate Full Stack Developer & Problem Solver crafting innovative digital solutions
          with modern technologies and creative problem-solving approaches.
        </p>
        <div class="hero-buttons">
          <a href="#contact" class="btn btn-primary">
            <i class="fas fa-envelope"></i>
            Get In Touch
          </a>
          <a href="./assets/Kaushal_Raj_Pandey_Resume (2) - Copy.docx" class="btn btn-secondary" download>
            <i class="fas fa-download"></i>
            Download CV
          </a>
        </div>
        <div class="social-links">
          <a href="mailto:<EMAIL>" class="social-link" title="Email">
            <i class="fas fa-envelope"></i>
          </a>
          <a href="https://www.linkedin.com/in/kaushal-raj-pandey" class="social-link" title="LinkedIn" target="_blank">
            <i class="fab fa-linkedin-in"></i>
          </a>
          <a href="https://github.com/kaushalrajp" class="social-link" title="GitHub" target="_blank">
            <i class="fab fa-github"></i>
          </a>
          <a href="https://leetcode.com/kaushalrajp" class="social-link" title="LeetCode" target="_blank">
            <i class="fas fa-code"></i>
          </a>
          <a href="https://auth.geeksforgeeks.org/user/kaushalrajp" class="social-link" title="GeeksforGeeks" target="_blank">
            <i class="fas fa-laptop-code"></i>
          </a>
        </div>
      </div>
    </div>
    <div class="scroll-indicator">
      <div class="scroll-arrow"></div>
    </div>
  </section>
  <!-- About Section -->
  <section id="about" class="about">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">About Me</h2>
        <p class="section-subtitle">Passionate developer with a drive for innovation</p>
      </div>
      <div class="about-content">
        <div class="about-text" data-aos="fade-right">
          <div class="about-card">
            <h3>Profile Summary</h3>
            <p>
              Result-driven <strong>Web Developer</strong> and <strong>Problem Solver</strong> with expertise in
              <strong>frontend & full-stack development</strong>. I have strong knowledge of Data Structures & Algorithms
              with <strong>300+ problems solved</strong>, and proven experience in hackathons & API integrations.
            </p>
            <div class="about-stats">
              <div class="stat-item">
                <div class="stat-number">300+</div>
                <div class="stat-label">DSA Problems</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">15+</div>
                <div class="stat-label">Projects</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">5+</div>
                <div class="stat-label">Hackathons</div>
              </div>
            </div>
          </div>
        </div>
        <div class="about-image" data-aos="fade-left">
          <div class="image-wrapper">
            <img src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=500&h=400&fit=crop" alt="Coding workspace">
            <div class="floating-elements">
              <div class="floating-icon"><i class="fab fa-react"></i></div>
              <div class="floating-icon"><i class="fab fa-js-square"></i></div>
              <div class="floating-icon"><i class="fab fa-python"></i></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Skills Section -->
  <section id="skills" class="skills">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">Technical Skills</h2>
        <p class="section-subtitle">Technologies I work with</p>
      </div>
      <div class="skills-content">
        <div class="skills-categories">
          <div class="skill-category" data-aos="fade-up" data-aos-delay="100">
            <div class="category-header">
              <i class="fas fa-code"></i>
              <h3>Programming Languages</h3>
            </div>
            <div class="skills-list">
              <div class="skill-item">
                <span class="skill-name">JavaScript</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="90%"></div>
                </div>
              </div>
              <div class="skill-item">
                <span class="skill-name">Python</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="85%"></div>
                </div>
              </div>
              <div class="skill-item">
                <span class="skill-name">Java</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="80%"></div>
                </div>
              </div>
              <div class="skill-item">
                <span class="skill-name">C++</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="75%"></div>
                </div>
              </div>
            </div>
          </div>

          <div class="skill-category" data-aos="fade-up" data-aos-delay="200">
            <div class="category-header">
              <i class="fas fa-laptop-code"></i>
              <h3>Web Development</h3>
            </div>
            <div class="skills-list">
              <div class="skill-item">
                <span class="skill-name">React.js</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="88%"></div>
                </div>
              </div>
              <div class="skill-item">
                <span class="skill-name">Node.js</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="82%"></div>
                </div>
              </div>
              <div class="skill-item">
                <span class="skill-name">HTML5/CSS3</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="95%"></div>
                </div>
              </div>
              <div class="skill-item">
                <span class="skill-name">Flask</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="78%"></div>
                </div>
              </div>
            </div>
          </div>

          <div class="skill-category" data-aos="fade-up" data-aos-delay="300">
            <div class="category-header">
              <i class="fas fa-database"></i>
              <h3>Database & Tools</h3>
            </div>
            <div class="skills-list">
              <div class="skill-item">
                <span class="skill-name">MongoDB</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="80%"></div>
                </div>
              </div>
              <div class="skill-item">
                <span class="skill-name">Firebase</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="85%"></div>
                </div>
              </div>
              <div class="skill-item">
                <span class="skill-name">Git</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="90%"></div>
                </div>
              </div>
              <div class="skill-item">
                <span class="skill-name">Postman</span>
                <div class="skill-bar">
                  <div class="skill-progress" data-width="88%"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Projects Section -->
  <section id="projects" class="projects">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">Featured Projects</h2>
        <p class="section-subtitle">Some of my recent work</p>
      </div>
      <div class="projects-grid">
        <div class="project-card" data-aos="fade-up" data-aos-delay="100">
          <div class="project-image">
            <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=250&fit=crop" alt="Medical System">
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                <a href="#" class="project-link"><i class="fab fa-github"></i></a>
              </div>
            </div>
          </div>
          <div class="project-content">
            <div class="project-category">Healthcare Tech</div>
            <h3 class="project-title">Sahyog – Medical Appointment System</h3>
            <p class="project-description">
              Advanced medical appointment system built with HTML, CSS, JS, Firebase Firestore & Google Calendar API.
              Achieved 3rd Position at Postman API Hackathon (BITS Pilani).
            </p>
            <div class="project-tech">
              <span class="tech-tag">JavaScript</span>
              <span class="tech-tag">Firebase</span>
              <span class="tech-tag">Google API</span>
            </div>
            <div class="project-achievement">
              <i class="fas fa-trophy"></i>
              3rd Position - Postman API Hackathon
            </div>
          </div>
        </div>

        <div class="project-card" data-aos="fade-up" data-aos-delay="200">
          <div class="project-image">
            <img src="https://images.unsplash.com/photo-**********-ce09059eeffa?w=400&h=250&fit=crop" alt="Room Finder">
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                <a href="#" class="project-link"><i class="fab fa-github"></i></a>
              </div>
            </div>
          </div>
          <div class="project-content">
            <div class="project-category">Real Estate</div>
            <h3 class="project-title">Stay Guide – PG & Room Finder</h3>
            <p class="project-description">
              Multi-page platform with advanced filters for budget & distance. Built with HTML, CSS, JS,
              later upgraded to React for enhanced user experience.
            </p>
            <div class="project-tech">
              <span class="tech-tag">React.js</span>
              <span class="tech-tag">CSS3</span>
              <span class="tech-tag">JavaScript</span>
            </div>
          </div>
        </div>

        <div class="project-card" data-aos="fade-up" data-aos-delay="300">
          <div class="project-image">
            <img src="https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&h=250&fit=crop" alt="Travel Planner">
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                <a href="#" class="project-link"><i class="fab fa-github"></i></a>
              </div>
            </div>
          </div>
          <div class="project-content">
            <div class="project-category">Travel Tech</div>
            <h3 class="project-title">Trip Zennie – Travel Planner</h3>
            <p class="project-description">
              All-in-one automated trip planning solution using Flask, Selenium web scraping,
              and Gemini LLM for intelligent recommendations.
            </p>
            <div class="project-tech">
              <span class="tech-tag">Flask</span>
              <span class="tech-tag">Selenium</span>
              <span class="tech-tag">Gemini LLM</span>
            </div>
          </div>
        </div>

        <div class="project-card" data-aos="fade-up" data-aos-delay="400">
          <div class="project-image">
            <img src="https://images.unsplash.com/photo-1466692476868-aef1dfb1e735?w=400&h=250&fit=crop" alt="Plant Recognition">
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                <a href="#" class="project-link"><i class="fab fa-github"></i></a>
              </div>
            </div>
          </div>
          <div class="project-content">
            <div class="project-category">Healthcare</div>
            <h3 class="project-title">Prayas – AYUSH Plants Platform</h3>
            <p class="project-description">
              Plant recognition platform with advanced filters & location mapping.
              Cleared 2 stages of Hack Heritage Hackathon with innovative AYUSH integration.
            </p>
            <div class="project-tech">
              <span class="tech-tag">Machine Learning</span>
              <span class="tech-tag">JavaScript</span>
              <span class="tech-tag">APIs</span>
            </div>
            <div class="project-achievement">
              <i class="fas fa-award"></i>
              Stage 2 Cleared - Hack Heritage
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Achievements Section -->
  <section id="achievements" class="achievements">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">Achievements & Recognition</h2>
        <p class="section-subtitle">Milestones in my journey</p>
      </div>
      <div class="achievements-content">
        <div class="achievements-grid">
          <div class="achievement-card" data-aos="zoom-in" data-aos-delay="100">
            <div class="achievement-icon">
              <i class="fas fa-trophy"></i>
            </div>
            <div class="achievement-content">
              <h3>3rd Position</h3>
              <p>Postman API Hackathon (BITS Pilani)</p>
              <span class="achievement-year">2024</span>
            </div>
          </div>

          <div class="achievement-card" data-aos="zoom-in" data-aos-delay="200">
            <div class="achievement-icon">
              <i class="fas fa-award"></i>
            </div>
            <div class="achievement-content">
              <h3>Stage 2 Cleared</h3>
              <p>Hack Heritage Hackathon (AYUSH Project)</p>
              <span class="achievement-year">2024</span>
            </div>
          </div>

          <div class="achievement-card" data-aos="zoom-in" data-aos-delay="300">
            <div class="achievement-icon">
              <i class="fas fa-crown"></i>
            </div>
            <div class="achievement-content">
              <h3>Winner</h3>
              <p>Code for Bharat Hackathon</p>
              <span class="achievement-year">2024</span>
            </div>
          </div>

          <div class="achievement-card" data-aos="zoom-in" data-aos-delay="400">
            <div class="achievement-icon">
              <i class="fas fa-code"></i>
            </div>
            <div class="achievement-content">
              <h3>300+ Problems</h3>
              <p>Data Structures & Algorithms</p>
              <span class="achievement-year">Ongoing</span>
            </div>
          </div>

          <div class="achievement-card" data-aos="zoom-in" data-aos-delay="500">
            <div class="achievement-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <div class="achievement-content">
              <h3>NDA SSB</h3>
              <p>Conferences - Allahabad (33) & Bhopal (21)</p>
              <span class="achievement-year">2021-2022</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Education Section -->
  <section id="education" class="education">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">Education</h2>
        <p class="section-subtitle">Academic background</p>
      </div>
      <div class="education-content">
        <div class="education-card" data-aos="fade-up">
          <div class="education-icon">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <div class="education-details">
            <h3>Bachelor of Technology</h3>
            <h4>Computer Science Engineering</h4>
            <p class="institution">Heritage Institute of Technology, Kolkata</p>
            <div class="education-meta">
              <span class="year">2023 - 2027</span>
              <span class="status">Currently in 2nd Year</span>
            </div>
            <p class="education-description">
              Pursuing B.Tech in Computer Science with focus on software development,
              data structures, algorithms, and modern web technologies.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="contact">
    <div class="container">
      <div class="section-header" data-aos="fade-up">
        <h2 class="section-title">Get In Touch</h2>
        <p class="section-subtitle">Let's work together on something amazing</p>
      </div>
      <div class="contact-content">
        <div class="contact-info" data-aos="fade-right">
          <div class="contact-card">
            <h3>Let's Connect!</h3>
            <p>
              I'm always excited to discuss new opportunities, innovative projects,
              or just have a chat about technology and development.
            </p>
            <div class="contact-details">
              <div class="contact-item">
                <div class="contact-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <div class="contact-text">
                  <h4>Email</h4>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
              </div>
              <div class="contact-item">
                <div class="contact-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="contact-text">
                  <h4>Location</h4>
                  <p>Kolkata, India</p>
                </div>
              </div>
            </div>
            <div class="social-links-contact">
              <a href="mailto:<EMAIL>" class="social-link-contact" title="Email">
                <i class="fas fa-envelope"></i>
                <span>Email</span>
              </a>
              <a href="https://www.linkedin.com/in/kaushal-raj-pandey" class="social-link-contact" title="LinkedIn" target="_blank">
                <i class="fab fa-linkedin-in"></i>
                <span>LinkedIn</span>
              </a>
              <a href="https://github.com/kaushalrajp" class="social-link-contact" title="GitHub" target="_blank">
                <i class="fab fa-github"></i>
                <span>GitHub</span>
              </a>
              <a href="https://leetcode.com/kaushalrajp" class="social-link-contact" title="LeetCode" target="_blank">
                <i class="fas fa-code"></i>
                <span>LeetCode</span>
              </a>
              <a href="https://auth.geeksforgeeks.org/user/kaushalrajp" class="social-link-contact" title="GeeksforGeeks" target="_blank">
                <i class="fas fa-laptop-code"></i>
                <span>GeeksforGeeks</span>
              </a>
            </div>
          </div>
        </div>
        <div class="contact-form" data-aos="fade-left">
          <form class="form">
            <div class="form-group">
              <input type="text" id="name" name="name" required>
              <label for="name">Your Name</label>
            </div>
            <div class="form-group">
              <input type="email" id="email" name="email" required>
              <label for="email">Your Email</label>
            </div>
            <div class="form-group">
              <input type="text" id="subject" name="subject" required>
              <label for="subject">Subject</label>
            </div>
            <div class="form-group">
              <textarea id="message" name="message" rows="5" required></textarea>
              <label for="message">Your Message</label>
            </div>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-paper-plane"></i>
              Send Message
            </button>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-text">
          <p>&copy; 2024 Kaushal Raj Pandey. All rights reserved.</p>
          <p>Designed & Developed with <i class="fas fa-heart"></i> by Kaushal</p>
        </div>
        <div class="footer-links">
          <a href="#home">Home</a>
          <a href="#about">About</a>
          <a href="#projects">Projects</a>
          <a href="#contact">Contact</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="script.js"></script>
</body>
</html>
